<?php

return [
    'services' => [
        'google_drive' => [
            'name' => 'Google Drive',
            'icon' => 'heroicon-o-folder',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => [
                // 'https://www.googleapis.com/auth/drive.file',
                // 'https://www.googleapis.com/auth/drive.metadata.readonly'
                'https://www.googleapis.com/auth/drive.readonly'
            ],
            'fields' => [
                'client_id' => env('GOOGLE_DRIVE_CLIENT_ID'),
                'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET'),
                'redirect_uri' => env('GOOGLE_DRIVE_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('GOOGLE_DRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
        'onedrive' => [
            'name' => 'OneDrive',
            'slug' => 'onedrive',
            'icon' => 'heroicon-o-cloud',
            'provider' => \LBCDev\OAuthManager\Providers\OneDriveProvider::class,
            'scopes' => [
                'https://graph.microsoft.com/Files.Read',
                'https://graph.microsoft.com/Files.ReadWrite',
                'https://graph.microsoft.com/User.Read'
            ],
            'fields' => [
                'client_id' => env('ONEDRIVE_CLIENT_ID'),
                'client_secret' => env('ONEDRIVE_CLIENT_SECRET'),
                'redirect_uri' => env('ONEDRIVE_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('ONEDRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
        // 'dropbox' => [
        //     'name' => 'Dropbox',
        //     'icon' => 'heroicon-o-cloud',
        //     'provider' => \LBCDev\OAuthManager\Providers\DropboxProvider::class,
        //     'scopes' => ['files.content.write', 'files.content.read'],
        //     'fields' => [
        //         'client_id' => 'App Key',
        //         'client_secret' => 'App Secret',
        //     ]
        // ],
        // 'youtube' => [
        //     'name' => 'YouTube',
        //     'icon' => 'heroicon-o-play',
        //     // 'provider' => \LBCDev\OAuthManager\Providers\YouTubeProvider::class,
        //     'scopes' => ['https://www.googleapis.com/auth/youtube.upload'],
        //     'fields' => [
        //         'client_id' => 'Client ID',
        //         'client_secret' => 'Client Secret',
        //     ]
        // ],
    ],

    'callback_route' => 'oauth-manager.callback',
    'middleware' => ['web'],
];
