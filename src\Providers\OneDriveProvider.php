<?php

namespace LBCDev\OAuthManager\Providers;

use Stevenma<PERSON><PERSON>\OAuth2\Client\Provider\Microsoft;
use League\OAuth2\Client\Token\AccessToken;
use GuzzleHttp\Client;
use Exception;

class OneDriveProvider extends BaseOAuthProvider
{
    protected function getProvider(): Microsoft
    {
        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;
        $clientSecret = $credentials['client_secret'] ?? null;

        if (!$clientId || !$clientSecret) {
            throw new Exception('Missing client credentials');
        }

        return new Microsoft([
            'clientId' => $clientId,
            'clientSecret' => $clientSecret,
            'redirectUri' => $this->getRedirectUri(),
        ]);
    }

    public function getAuthorizationUrl(): string
    {
        $provider = $this->getProvider();

        $authUrl = $provider->getAuthorizationUrl([
            'scope' => $this->config['scopes'],
        ]);

        session(['oauth2state' => $provider->getState()]);

        return $authUrl;
    }

    public function handleCallback(string $code): array
    {
        $provider = $this->getProvider();

        $token = $provider->getAccessToken('authorization_code', [
            'code' => $code,
        ]);

        return [
            'access_token' => $token->getToken(),
            'refresh_token' => $token->getRefreshToken(),
            'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
        ];
    }

    public function refreshToken(): ?array
    {
        if (!$this->service->refresh_token) {
            return null;
        }

        try {
            $provider = $this->getProvider();

            $token = $provider->getAccessToken('refresh_token', [
                'refresh_token' => $this->service->refresh_token,
            ]);

            return [
                'access_token' => $token->getToken(),
                'refresh_token' => $token->getRefreshToken(),
                'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
            ];
        } catch (Exception $e) {
            return null;
        }
    }

    public function revokeToken(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = new Client();
            
            // Microsoft Graph revoke endpoint
            $response = $client->post('https://graph.microsoft.com/v1.0/me/revokeSignInSessions', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return $response->getStatusCode() === 200;
        } catch (Exception $e) {
            return false;
        }
    }

    public function testConnection(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = new Client();
            
            // Test connection by getting user profile
            $response = $client->get('https://graph.microsoft.com/v1.0/me', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return $response->getStatusCode() === 200;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get OneDrive files and folders
     */
    public function getFiles(string $folderId = 'root'): array
    {
        if (!$this->service->access_token) {
            throw new Exception('No access token available');
        }

        try {
            $client = new Client();
            
            $endpoint = $folderId === 'root' 
                ? 'https://graph.microsoft.com/v1.0/me/drive/root/children'
                : "https://graph.microsoft.com/v1.0/me/drive/items/{$folderId}/children";

            $response = $client->get($endpoint, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            return $data['value'] ?? [];
        } catch (Exception $e) {
            throw new Exception('Failed to get OneDrive files: ' . $e->getMessage());
        }
    }

    /**
     * Get OneDrive user info
     */
    public function getUserInfo(): array
    {
        if (!$this->service->access_token) {
            throw new Exception('No access token available');
        }

        try {
            $client = new Client();
            
            $response = $client->get('https://graph.microsoft.com/v1.0/me', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->service->access_token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (Exception $e) {
            throw new Exception('Failed to get user info: ' . $e->getMessage());
        }
    }
}
